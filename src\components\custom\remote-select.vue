<script setup lang="ts">
import { ref, withDefaults, computed } from 'vue';
import { compact } from 'lodash-es';

defineOptions({
  name: 'RemoteSelect',
  inheritAttrs: false
});

const props = withDefaults(defineProps<{
  /** the api function */
  apiFn: (params: any) => Promise<any>;
  /** the options */
  options?: any[];
  /** additional filter to merge with api call */
  filter?: Record<string, any>;
  /** default value */
  defaultValue?: number | string;
}>(), {
  defaultValue: 0
});

const value = defineModel<number | string | null | undefined>('value', { required: true });

const loading = ref(false);

const remoteOptions = ref<any[]>(compact(props.options) || []);

// 计算显示值：当值等于默认值时显示空字符串
const displayValue = computed({
  get: () => value.value === props.defaultValue ? null : value.value,
  set: (val) => {
    value.value = val === null ? props.defaultValue : val;
  }
});

const onSearch = async (q: string) => {
  loading.value = true;
  try {
    const { data } = await props.apiFn({
      _page: 1,
      _limit: 10,
      status: true,
      q,
      ...props.filter
    });
    remoteOptions.value = data.records;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(error);
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <NSelect
    v-model:value="displayValue"
    :options="remoteOptions"
    :loading="loading"
    v-bind="$attrs"
    remote
    filterable
    clearable
    @search="onSearch"
  />
</template>

<style scoped></style>
